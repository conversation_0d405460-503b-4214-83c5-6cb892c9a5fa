<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="仪器设备安装调试记录" okText="确认" @ok="handleSubmit" :width="1400">
    <div class="table-container">
      <div class="table-header">
        <div class="table-actions">
          <button class="action-btn" @click="printTable">打印</button>
        </div>
      </div>
      <table :id="printId" border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse">
        <thead>
          <tr>
            <th style="text-align: center; font-size: 16px; font-weight: bold; padding: 15px;" colspan="4">仪器设备安装调试记录</th>
          </tr>
        </thead>
        <tbody>
          <!-- 第一行：名称、编号 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">名称</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.equipmentName || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">编号</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.equipmentNumber || '' }}</td>
          </tr>

          <!-- 第二行：型号、国别 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">型号</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.model || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">国别</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.country || '' }}</td>
          </tr>

          <!-- 第三行：制造厂、出厂编号 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">制造厂</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.manufacturer || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">出厂编号</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.serialNumber || '' }}</td>
          </tr>

          <!-- 第四行：调试人、调试日期 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">调试人</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.debugger || '' }}</td>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold;">调试日期</td>
            <td style="width: 35%; text-align: left; padding: 8px;">{{ formData.debugDate || '' }}</td>
          </tr>

          <!-- 第五行：调试记录 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold; vertical-align: top;">调试记录</td>
            <td colspan="3" style="text-align: left; padding: 8px; height: 300px; vertical-align: top;">
              <div style="white-space: pre-wrap; min-height: 280px;">{{ formData.debugRecord || '' }}</div>
            </td>
          </tr>

          <!-- 第六行：调试结论 -->
          <tr>
            <td style="width: 15%; text-align: center; padding: 8px; background-color: #f5f5f5; font-weight: bold; vertical-align: top;">调试结论</td>
            <td colspan="3" style="text-align: left; padding: 8px; height: 120px; vertical-align: top;">
              <div style="white-space: pre-wrap; min-height: 60px;">{{ formData.debugConclusion || '' }}</div>
              <div style="margin-top: 20px; display: flex; justify-content: space-between;">
                <span>调试人：{{ formData.debuggerSignature || '' }}</span>
                <span>日期：{{ formData.debuggerDate || '' }}</span>
              </div>
            </td>
          </tr>

          <!-- 第七行：技术负责人 -->
          <tr>
            <td colspan="4" style="text-align: left; padding: 8px; height: 80px;">
              <div style="display: flex; justify-content: space-between; align-items: flex-end; height: 100%;">
                <span>技术负责人：{{ formData.techManagerSignature || '' }}</span>
                <span>日期：{{ formData.techManagerDate || '' }}</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';

const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);

// 定义表单数据接口
interface FormDataType {
  // 基本信息
  equipmentName: string;           // 设备名称
  equipmentNumber: string;         // 设备编号
  model: string;                   // 型号
  country: string;                 // 国别
  manufacturer: string;            // 制造厂
  serialNumber: string;            // 出厂编号

  // 调试信息
  debugger: string;                // 调试人
  debugDate: string;               // 调试日期
  debugRecord: string;             // 调试记录
  debugConclusion: string;         // 调试结论
  debuggerSignature: string;       // 调试人签字
  debuggerDate: string;            // 调试人签字日期

  // 技术负责人签字信息
  techManagerSignature: string;   // 技术负责人签字
  techManagerDate: string;        // 技术负责人签字日期
}

// 定义表单数据
const formData = reactive<FormDataType>({
  // 基本信息
  equipmentName: '',
  equipmentNumber: '',
  model: '',
  country: '',
  manufacturer: '',
  serialNumber: '',

  // 调试信息
  debugger: '',
  debugDate: '',
  debugRecord: '',
  debugConclusion: '',
  debuggerSignature: '',
  debuggerDate: '',

  // 技术负责人签字信息
  techManagerSignature: '',
  techManagerDate: '',
});

// 重置表单数据
const resetFormData = () => {
  // 基本信息
  formData.equipmentName = '';
  formData.equipmentNumber = '';
  formData.model = '';
  formData.country = '';
  formData.manufacturer = '';
  formData.serialNumber = '';

  // 调试信息
  formData.debugger = '';
  formData.debugDate = '';
  formData.debugRecord = '';
  formData.debugConclusion = '';
  formData.debuggerSignature = '';
  formData.debuggerDate = '';

  // 技术负责人签字信息
  formData.techManagerSignature = '';
  formData.techManagerDate = '';
};

//表单赋值
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  console.log("🚀 ~ data:", data)
  printId.value = buildUUID().toString();

  // 先重置表单数据
  resetFormData();

  if (data && data.record) {
    // 如果有传入数据，则赋值
    // 基本信息
    formData.equipmentName = data.record.equipmentName || '';
    formData.equipmentNumber = data.record.equipmentNumber || '';
    formData.model = data.record.model || '';
    formData.country = data.record.country || '';
    formData.manufacturer = data.record.manufacturer || '';
    formData.serialNumber = data.record.serialNumber || '';

    // 调试信息
    formData.debugger = data.record.debugger || '';
    formData.debugDate = data.record.debugDate || '';
    formData.debugRecord = data.record.debugRecord || '';
    formData.debugConclusion = data.record.debugConclusion || '';
    formData.debuggerSignature = data.record.debuggerSignature || '';
    formData.debuggerDate = data.record.debuggerDate || '';

    // 技术负责人签字信息
    formData.techManagerSignature = data.record.techManagerSignature || '';
    formData.techManagerDate = data.record.techManagerDate || '';
  }
});

// 打印表格
function printTable() {
  printJS({
    type: 'html',
    printable: printId.value,
    scanStyles: false,
  });
}

async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}


.action-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

table {
  border: 1px solid #ccc;
  width: 100%;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 8px;
  height: 40px;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
