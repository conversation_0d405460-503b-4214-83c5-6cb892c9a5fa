<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="60%">
    <!-- 分步表单 -->
    <a-steps :current="currentStep" class="steps-container">
      <a-step title="验收信息" description="填写仪器验收相关信息" />
      <a-step title="补充信息" description="填写补充信息" />
    </a-steps>

    <!-- 第一步：验收信息表单 -->
    <div v-show="currentStep === 0">
      <a-form
          ref="formRef"
          :model="orderMainModel"
          @submit="handleSubmit"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :rules="validatorRules">

        <a-row class="form-row" :gutter="8">
          <a-col :span="20">
            <a-form-item label="接收时状态" name="receiveStatus">
              <a-select v-model:value="orderMainModel.receiveStatus">
                <a-select-option value="1">全新</a-select-option>
                <a-select-option value="2">用过</a-select-option>
                <a-select-option value="3">改装</a-select-option>
                <a-select-option value="4">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        <a-col :span="20" v-if="orderMainModel.receiveStatus == '4'">
          <a-form-item label="接收时状态其他" name="receiveStatusOther">
            <a-input v-model:value="orderMainModel.receiveStatusOther" placeholder="其他" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="外观" name="appearanceContent">
            <a-select mode="multiple" v-model:value="orderMainModel.appearanceContent">
              <a-select-option value="1">无破损</a-select-option>
              <a-select-option value="2">无变形</a-select-option>
              <a-select-option value="3">标识齐全清晰</a-select-option>
              <a-select-option value="4">其他</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="20" v-if="orderMainModel.appearanceContent.indexOf('4') !== -1">
          <a-form-item label="外观其他" name="appearanceOther">
            <a-input v-model:value="orderMainModel.appearanceOther" placeholder="其他" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="仪器调试" name="insTestContent">
            <a-select mode="multiple" v-model:value="orderMainModel.insTestContent">
              <a-select-option value="1">开机正常</a-select-option>
              <a-select-option value="2">调试正常</a-select-option>
              <a-select-option value="3">异常</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="20" v-if="orderMainModel.insTestContent.indexOf('3') !== -1">
          <a-form-item label="仪器调试异常" name="insTestOther">
            <a-input v-model:value="orderMainModel.insTestOther" placeholder="异常" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="检定证书" name="certificate">
            <a-select v-model:value="orderMainModel.certificate">
              <a-select-option value="1">齐全有效</a-select-option>
              <a-select-option value="2">欠缺/失效</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="20" v-if="orderMainModel.certificate == '2'">
          <a-form-item label="欠缺/失效内容" name="certificateOther">
            <a-input v-model:value="orderMainModel.certificateOther" placeholder="其他" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="文件资料" name="documentInformationContent">
            <a-select mode="multiple" v-model:value="orderMainModel.documentInformationContent">
              <a-select-option value="1">合格证</a-select-option>
              <a-select-option value="2">说明书</a-select-option>
              <a-select-option value="3">保修单</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="配件:种类数量与装箱单相符" name="accessory">
            <a-select v-model:value="orderMainModel.accessory">
              <a-select-option value="1">是</a-select-option>
              <a-select-option value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="20" v-if="orderMainModel.accessory == '0'">
          <a-form-item label="内容" name="accessoryOther">
            <a-input v-model:value="orderMainModel.accessoryOther" placeholder="内容" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="其他" name="otherContent">
            <a-textarea style="width:100%" v-model:value="orderMainModel.otherContent" show-count :maxlength="80" rows="3" placeholder="其他" />
          </a-form-item>
        </a-col>

        <a-col :span="20">
          <a-form-item label="验收结论" name="acceptConclusion">
            <a-textarea style="width:100%" v-model:value="orderMainModel.acceptConclusion" show-count :maxlength="200" rows="3" placeholder="验收结论" />
          </a-form-item>
        </a-col>
          <a-col :span="20">
            <a-form-item label="上传文件" name="fileUrl">
              <JUpload v-model:value="orderMainModel.fileUrl" bizPath="insAccept" :uploadGoOn="false" :beforeUpload="extendBeforeUpload"></JUpload>
            </a-form-item>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 第二步：补充信息表单 -->
    <div v-show="currentStep === 1">
      <a-form
          ref="formRef2"
          :model="orderMainModel"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :rules="validatorRules">

        <a-row class="form-row" :gutter="8">
          <a-col :span="20">
            <a-form-item label="补充说明" name="additionalInfo">
              <a-input v-model:value="orderMainModel.additionalInfo" placeholder="请输入补充说明" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 分步操作按钮 -->
    <template #footer>
      <a-space>
        <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
        <a-button v-if="currentStep < 1" type="primary" @click="nextStep">下一步</a-button>
        <a-button v-if="currentStep === 1" type="primary" @click="handleSubmit">提交</a-button>
        <a-button @click="closeModal">取消</a-button>
      </a-space>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref, watch } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { saveAccept } from '../instrumentManagement.api';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
import type { FormInstance } from 'ant-design-vue';

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref<FormInstance>();
const formRef2 = ref<FormInstance>();
const isUpdate = ref(true);
const isFooter = ref(true);
// 分步表单当前步骤
const currentStep = ref(0);
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 7 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 16 },
});
const orderMainModel = reactive({
  id: null,
  parentId: null,
  receiveStatus: null,
  receiveStatusOther: null,
  appearance: '',
  appearanceContent: [],
  appearanceOther: null,
  insTest: '',
  insTestContent: [],
  insTestOther: null,
  certificate: null,
  certificateOther: null,
  documentInformation: '',
  documentInformationContent: [],
  accessory: null,
  accessoryOther: null,
  otherContent: null,
  acceptConclusion: null,
  fileUrl: null,
  status: null,
  // 新增第二步字段
  additionalInfo: null,
});
const validatorRules = {
  receiveStatus: [{ required: true, message: '必选！' }],
  receiveStatusOther: [
    { required: true, message: '必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  ],
  appearanceContent: [{ required: true, message: '必选！' }],
  appearanceOther: [
    { required: true, message: '必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  ],
  
  insTestContent: [{ required: true, message: '必选！' }],
  insTestOther: [
    { required: true, message: '必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  ],
  certificate: [{ required: true, message: '必选！' }],
  certificateOther: [
    { required: true, message: '必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  ],
  documentInformationContent: [{ required: true, message: '必选！' }],
  accessory: [{ required: true, message: '必选！' }],
  accessoryOther: [
    { required: true, message: '必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  ],
  acceptConclusion: [
    { required: true, message: '必填！' },
    { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  ],
  // otherContent: [
  //   { required: true, message: '必填！' },
  //   { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }
  // ],
  
};
//监听
watch(
  [
    () => orderMainModel.receiveStatus,
    () => orderMainModel.appearance,
    () => orderMainModel.insTest,
    () => orderMainModel.certificate,
    () => orderMainModel.accessory
  ], (newVal, oldVal) => {
    console.log("🚀 ~ newVal, oldVal:", newVal, oldVal)
    if(newVal[0] !== '4') {
      orderMainModel.receiveStatusOther = null
    }
    if(newVal[1].indexOf('4') != -1) {
      orderMainModel.appearanceOther = null
    }
    if(newVal[2].indexOf('3') != -1) {
      orderMainModel.insTestOther = null
    }
    if(newVal[3] !== '2') {
      orderMainModel.certificateOther = null
    }
    if(newVal[4] !== '0') {
      orderMainModel.accessoryOther = null
    }
  }
)
//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  reset();
  // 重置步骤
  currentStep.value = 0;
  setModalProps({ confirmLoading: false, showCancelBtn: false, showOkBtn: false });
  isUpdate.value = !!data?.isUpdate;
  isFooter.value = !!data?.showFooter;
  if (unref(isUpdate)) {
    //表单赋值
    // await setFieldsValue({
    //     ...data.record,
    // });
    Object.assign(orderMainModel, data.record);
  }
  console.log('🚀 ~ file:  ~ orderMainModel:', orderMainModel);
});
//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));

// 分步表单方法
function nextStep() {
  // 验证第一步表单
  formRef.value?.validate().then(() => {
    currentStep.value++;
  }).catch((error) => {
    console.log('第一步验证失败', error);
  });
}

function prevStep() {
  currentStep.value--;
}
//表单提交事件
function handleSubmit() {
  // 验证所有表单
  Promise.all([
    formRef.value?.validate(),
    formRef2.value?.validate()
  ]).then(async () => {
    try {
      // let values = await validate();
      // if(orderMainModel.employeeExamFileDOS.length <= 0) {
      //   message.warning('');
      //   return ;
      // }
      orderMainModel.appearance = orderMainModel.appearanceContent.join(',')
      orderMainModel.insTest = orderMainModel.insTestContent.join(',')
      orderMainModel.documentInformation = orderMainModel.documentInformationContent.join(',')
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveAccept(orderMainModel);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success', { isUpdate: isUpdate.value, orderMainModel });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }).catch((error: any) => {
    console.log('表单验证失败', error);
  });
}
function reset() {
    orderMainModel.id = null;
    orderMainModel.parentId = null;
    orderMainModel.receiveStatus = null;
    orderMainModel.receiveStatusOther = null;
    orderMainModel.appearance = '';
    orderMainModel.appearanceContent = [];
    orderMainModel.appearanceOther = null;
    orderMainModel.insTest = '';
    orderMainModel.insTestContent = [];
    orderMainModel.insTestOther = null;
    orderMainModel.certificate = null;
    orderMainModel.certificateOther = null;
    orderMainModel.documentInformation = '';
    orderMainModel.documentInformationContent = [];
    orderMainModel.accessory = null;
    orderMainModel.accessoryOther = null;
    orderMainModel.otherContent = null;
    orderMainModel.acceptConclusion = null;
    orderMainModel.fileUrl = null;
    orderMainModel.status = null;
    // 重置新增字段
    orderMainModel.additionalInfo = null;
}

function extendBeforeUpload(pro) {
  console.log('beforeload',pro)
  console.log('beforeloadType',pro.type)
  
  if(pro.type != 'application/pdf' && pro.type != 'application/zip' && pro.name.indexOf('.rar') == -1 && pro.name.indexOf('.7z') == -1) {
    console.log('orderMainModel.fileUrl',orderMainModel.fileUrl)
    message.warning('请上传 PDF 或 压缩包 文件');
    return false;
  }
}
</script>

<style lang="less" scoped>
.fontColor {
    color: black;
}
</style>