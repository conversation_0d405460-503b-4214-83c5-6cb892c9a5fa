<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="70%">
    <a-form
        ref="formRef"
        :model="procurementModel"
        @submit="handleSubmit"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :rules="validatorRules">

      <a-row class="form-row" :gutter="16">
        <!-- 申购类型 -->
        <a-col :span="20">
          <a-form-item label="申购类型" name="procurementType">
            <a-select v-model:value="procurementModel.procurementType" placeholder="请选择申购类型">
              <a-select-option value="新购">新购</a-select-option>
              <a-select-option value="更新">更新</a-select-option>
              <a-select-option value="补充">补充</a-select-option>
              <a-select-option value="维修">维修</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 申购资产名称 -->
        <a-col :span="20">
          <a-form-item label="申购资产名称" name="assetName">
            <a-input v-model:value="procurementModel.assetName" placeholder="请输入申购资产名称" />
          </a-form-item>
        </a-col>

        <!-- 规格 -->
        <a-col :span="20">
          <a-form-item label="规格" name="specification">
            <a-input v-model:value="procurementModel.specification" placeholder="请输入规格" />
          </a-form-item>
        </a-col>

        <!-- 数量 -->
        <a-col :span="20">
          <a-form-item label="数量" name="quantity">
            <a-input-number
              v-model:value="procurementModel.quantity"
              placeholder="请输入数量"
              :min="1"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>

        <!-- 单位 -->
        <a-col :span="20">
          <a-form-item label="单位" name="unit">
            <a-select v-model:value="procurementModel.unit" placeholder="请选择单位">
              <a-select-option value="台">台</a-select-option>
              <a-select-option value="套">套</a-select-option>
              <a-select-option value="个">个</a-select-option>
              <a-select-option value="件">件</a-select-option>
              <a-select-option value="批">批</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 预估单价 -->
        <a-col :span="20">
          <a-form-item label="预估单价" name="estimatedUnitPrice">
            <a-input-number
              v-model:value="procurementModel.estimatedUnitPrice"
              placeholder="请输入预估单价"
              :min="0"
              :precision="2"
              style="width: 100%"
              @change="calculateTotalAmount"
            />
          </a-form-item>
        </a-col>

        <!-- 预估金额 -->
        <a-col :span="20">
          <a-form-item label="预估金额" name="estimatedTotalAmount">
            <a-input-number
              v-model:value="procurementModel.estimatedTotalAmount"
              placeholder="预估金额"
              :min="0"
              :precision="2"
              style="width: 100%"
              disabled
            />
          </a-form-item>
        </a-col>

        <!-- 请购原因 -->
        <a-col :span="20">
          <a-form-item label="请购原因" name="procurementReason">
            <a-textarea
              v-model:value="procurementModel.procurementReason"
              placeholder="请输入请购原因"
              :rows="3"
              show-count
              :maxlength="500"
            />
          </a-form-item>
        </a-col>

        <!-- 技术指标 -->
        <a-col :span="20">
          <a-form-item label="技术指标" name="technicalIndicators">
            <a-textarea
              v-model:value="procurementModel.technicalIndicators"
              placeholder="请输入技术指标（选填）"
              :rows="3"
              show-count
              :maxlength="1000"
            />
          </a-form-item>
        </a-col>

        <!-- 功能需求 -->
        <a-col :span="20">
          <a-form-item label="功能需求" name="functionalRequirements">
            <a-textarea
              v-model:value="procurementModel.functionalRequirements"
              placeholder="请输入功能需求（选填）"
              :rows="3"
              show-count
              :maxlength="1000"
            />
          </a-form-item>
        </a-col>

        <!-- 资产使用目的 -->
        <a-col :span="20">
          <a-form-item label="资产使用目的" name="assetUsagePurpose">
            <a-textarea
              v-model:value="procurementModel.assetUsagePurpose"
              placeholder="请输入资产使用目的"
              :rows="2"
              show-count
              :maxlength="500"
            />
          </a-form-item>
        </a-col>

        <!-- 资金支出属性 -->
        <a-col :span="20">
          <a-form-item label="资金支出属性" name="fundingAttribute">
            <a-select v-model:value="procurementModel.fundingAttribute" placeholder="请选择资金支出属性">
              <a-select-option value="运营费用">运营费用</a-select-option>
              <a-select-option value="资本支出">资本支出</a-select-option>
              <a-select-option value="研发费用">研发费用</a-select-option>
              <a-select-option value="维护费用">维护费用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 资产要求到位时间 -->
        <a-col :span="20">
          <a-form-item label="资产要求到位时间" name="requiredDeliveryDate">
            <a-date-picker
              v-model:value="procurementModel.requiredDeliveryDate"
              placeholder="请选择资产要求到位时间"
              style="width: 100%"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>

      </a-row>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref, watch } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { saveProcurementForm } from '../instrumentManagement.api';

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref<FormInstance>();
const isUpdate = ref(false);
const isFooter = ref(true);

// 表单布局配置
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 6 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 18 },
});

// 申购表单数据模型
const procurementModel = reactive({
  id: null as string | null,
  procurementType: null as string | null,        // 申购类型*
  assetName: null as string | null,              // 申购资产名称*
  specification: null as string | null,          // 规格*
  quantity: null as number | null,               // 数量*
  unit: null as string | null,                   // 单位*
  estimatedUnitPrice: null as number | null,     // 预估单价*
  estimatedTotalAmount: null as number | null,   // 预估金额*
  procurementReason: null as string | null,      // 请购原因*
  technicalIndicators: null as string | null,    // 技术指标
  functionalRequirements: null as string | null, // 功能需求
  assetUsagePurpose: null as string | null,      // 资产使用目的*
  fundingAttribute: null as string | null,       // 资金支出属性*
  requiredDeliveryDate: null as string | null,   // 资产要求到位时间
});

// 表单验证规则
const validatorRules = {
  procurementType: [{ required: true, message: '请选择申购类型！' }],
  assetName: [{ required: true, message: '请输入申购资产名称！' }],
  specification: [{ required: true, message: '请输入规格！' }],
  quantity: [{ required: true, message: '请输入数量！' }],
  unit: [{ required: true, message: '请选择单位！' }],
  estimatedUnitPrice: [{ required: true, message: '请输入预估单价！' }],
  estimatedTotalAmount: [{ required: true, message: '预估金额不能为空！' }],
  procurementReason: [{ required: true, message: '请输入请购原因！' }],
  assetUsagePurpose: [{ required: true, message: '请输入资产使用目的！' }],
  fundingAttribute: [{ required: true, message: '请选择资金支出属性！' }],
};

// 计算预估金额
const calculateTotalAmount = () => {
  if (procurementModel.quantity && procurementModel.estimatedUnitPrice) {
    procurementModel.estimatedTotalAmount = Number((procurementModel.quantity * procurementModel.estimatedUnitPrice).toFixed(2));
  } else {
    procurementModel.estimatedTotalAmount = null;
  }
};

// 监听数量变化，自动计算金额
watch(() => procurementModel.quantity, () => {
  calculateTotalAmount();
});

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  isFooter.value = !!data?.showFooter;
  if (unref(isUpdate)) {
    //表单赋值
    Object.assign(procurementModel, data.record);
    procurementModel.assetName = data.record.instrumentName;
    procurementModel.specification = data.record.instrumentModel;
    procurementModel.estimatedUnitPrice = data.record.price;
  }
  console.log('🚀 ~ procurementModal ~ procurementModel:', procurementModel);
});

//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增申购' : '编辑申购'));

//表单提交事件
function handleSubmit() {
  if (!formRef.value) return;

  formRef.value
    .validate()
    .then(async () => {
      try {
        setModalProps({ confirmLoading: true });

        // 提交申购表单数据
        await saveProcurementForm(procurementModel, isUpdate.value);
        console.log('提交申购数据:', procurementModel);
        message.success(isUpdate.value ? '申购信息更新成功！' : '申购信息提交成功！');

        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, procurementModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('表单验证失败:', error);
    });
}

// 重置表单
function reset() {
  procurementModel.id = null;
  procurementModel.procurementType = null;
  procurementModel.assetName = null;
  procurementModel.specification = null;
  procurementModel.quantity = null;
  procurementModel.unit = null;
  procurementModel.estimatedUnitPrice = null;
  procurementModel.estimatedTotalAmount = null;
  procurementModel.procurementReason = null;
  procurementModel.technicalIndicators = null;
  procurementModel.functionalRequirements = null;
  procurementModel.assetUsagePurpose = null;
  procurementModel.fundingAttribute = null;
  procurementModel.requiredDeliveryDate = null;
}
</script>

<style lang="less" scoped>
.form-row {
  margin-bottom: 16px;
}

.fontColor {
  color: black;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}
</style>